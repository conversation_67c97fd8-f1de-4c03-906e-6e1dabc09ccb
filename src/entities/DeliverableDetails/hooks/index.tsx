import { useEffect, useRef, useState } from 'react';
import {
  Activity,
  BarChartLineFill,
  Calculator,
  FileText,
  Globe,
  ListCheck,
  Percent,
  Person,
  PlusCircle,
  Search,
  Trophy,
} from 'react-bootstrap-icons';
import { useRouter } from 'next/router';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTranslate } from '@tolgee/react';
import html2canvas from 'html2canvas';

import { useToast } from '~/app/contexts/ToastContext';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import deliverablesService from '~/shared/services/deliverables';
import {
  DeliverableItem,
  DeliverableTypeEnum,
} from '~/shared/types/Deliverable';
import { capitalizeFirstWord } from '~/shared/utils/capitalizeWord';
import { CatalogItemDetailsCardContentTypesEnum } from '~/shared/utils/enums';

import { DELIVERABLE_MAPPED_TYPES } from '../constants';
import { CatalogItemCardData } from '../types';

export function useCatalogItemsDetails(catalogItemId?: string) {
  const { t } = useTranslate();
  const queryClient = useQueryClient();
  const toast = useToast();
  const actionModal = useActionModal();
  const successModal = useActionModal();
  const router = useRouter();
  const gridRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [transformedData, setTransformedData] =
    useState<Array<CatalogItemCardData>>();
  const kpiDataSession = sessionStorage.getItem('kpi-data');
  const kpiData: DeliverableItem = kpiDataSession && JSON.parse(kpiDataSession);
  const kpiDataFiltered = kpiData && kpiData.uid === catalogItemId;

  const { data, isLoading } = useQuery({
    queryKey: ['getDeliverableDetails', catalogItemId],
    queryFn: () =>
      deliverablesService.getDeliverableById(catalogItemId as string),
    enabled: !kpiDataFiltered || !!catalogItemId,
    cacheTime: 0,
  });

  useEffect(() => {
    if (!kpiData && !data) {
      return;
    }

    const dataFiltered = kpiDataFiltered ? kpiData : data;
    const deliverableType = dataFiltered?.type;

    const mappedType =
      DELIVERABLE_MAPPED_TYPES[
        dataFiltered?.type as keyof typeof DeliverableTypeEnum
      ];

    if (!dataFiltered) {
      setTransformedData([]);
    }
    setTransformedData([
      {
        info: 'TYPE',
        content: capitalizeFirstWord(mappedType ?? ''),
        cardType: CatalogItemDetailsCardContentTypesEnum.SIMPLE,
        icon:
          deliverableType === DeliverableTypeEnum.KPI ? (
            <BarChartLineFill />
          ) : (
            <ListCheck />
          ),
      },
      {
        info: 'FUNCTION',
        content: capitalizeFirstWord(dataFiltered?.businessFunction ?? ''),
        cardType: CatalogItemDetailsCardContentTypesEnum.SIMPLE,
        icon: <Percent />,
      },
      {
        info: 'FREQUENCY',
        content: capitalizeFirstWord(dataFiltered?.frequency ?? ''),
        cardType: CatalogItemDetailsCardContentTypesEnum.SIMPLE,
        icon: <Activity />,
      },
      {
        info: 'USAGE',
        content: dataFiltered?.usage || 0,
        cardType: CatalogItemDetailsCardContentTypesEnum.SIMPLE,
        icon: <Person />,
      },
      {
        info: 'DEFINITION',
        content: dataFiltered?.definition,
        cardType: CatalogItemDetailsCardContentTypesEnum.TEXT,
        icon: <FileText />,
      },
      {
        info: 'DATA SOURCE',
        content: dataFiltered?.dataSource,
        cardType: CatalogItemDetailsCardContentTypesEnum.TEXT,
        icon: <Search />,
      },
      {
        info: 'BUSINESS LEVEL AGGREGATION',
        content: dataFiltered?.buLevelAggregation,
        cardType: CatalogItemDetailsCardContentTypesEnum.TEXT,
        icon: <PlusCircle />,
      },
      {
        info: 'GLOBAL OWNER',
        content: dataFiltered?.owners,
        cardType: CatalogItemDetailsCardContentTypesEnum.USER_LIST,
        icon: <Globe />,
      },
      ...(deliverableType === DeliverableTypeEnum.KPI
        ? [
            {
              info: 'CALCULATION METHOD',
              content: dataFiltered?.calculationMethod,
              cardType: CatalogItemDetailsCardContentTypesEnum.TEXT,
              icon: <Calculator />,
            },
          ]
        : []),
      ...(deliverableType === DeliverableTypeEnum.PROJECT_YES_NO
        ? [
            {
              info: 'DELIVERABLE',
              content: dataFiltered?.calculationMethod,
              cardType: CatalogItemDetailsCardContentTypesEnum.TEXT,
              icon: <Calculator />,
            },
          ]
        : []),
      ...((deliverableType === DeliverableTypeEnum.PROJECT ||
        deliverableType === DeliverableTypeEnum.MASTER_PROJECT) &&
      dataFiltered?.deliverables?.length
        ? [
            {
              info: 'DELIVERABLES',
              content: dataFiltered?.deliverables,
              cardType: CatalogItemDetailsCardContentTypesEnum.TEXT,
              icon: <Calculator />,
            },
          ]
        : []),
      {
        info: 'PA VALUE',
        content: dataFiltered?.paValue,
        cardType: CatalogItemDetailsCardContentTypesEnum.TEXT,
        icon: <Trophy />,
      },
    ]);
  }, [data, kpiDataFiltered]);

  const handleNavigateToEdit = async () => {
    if (catalogItemId) {
      await router.push(`/deliverables/edit/${catalogItemId}`);
    }
  };

  const handleNavigateBack = () => {
    router.push('/');
  };

  const handleDownloadSnapshot = async () => {
    if (!contentRef.current || !gridRef.current || !data) {
      return;
    }

    try {
      await new Promise(resolve => setTimeout(resolve, 100));

      const tempContainer = document.createElement('div');
      tempContainer.style.padding = '20px';
      tempContainer.style.fontFamily = 'system-ui, -apple-system, sans-serif';

      const titleClone = contentRef.current.cloneNode(true) as HTMLElement;
      titleClone.style.marginBottom = '24px'; // gap-6 equivalent

      const gridClone = gridRef.current.cloneNode(true) as HTMLElement;

      tempContainer.appendChild(titleClone);
      tempContainer.appendChild(gridClone);

      tempContainer.style.position = 'absolute';
      tempContainer.style.left = '-9999px';
      tempContainer.style.top = '-9999px';
      document.body.appendChild(tempContainer);

      const canvas = await html2canvas(tempContainer, {
        useCORS: true,
        allowTaint: true,
      });

      document.body.removeChild(tempContainer);

      canvas.toBlob(
        blob => {
          if (!blob) {
            return;
          }

          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${data.name || 'catalog-item'}-cards-snapshot-${
            new Date().toISOString().split('T')[0]
          }.png`;

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          URL.revokeObjectURL(url);
        },
        'image/png',
        1.0,
      );
    } catch (error) {
      console.error('Error generating screenshot:', error);
    }
  };

  const handledeleteDeliverable = () => {
    actionModal.openModal({
      title: t('common_warning'),
      message: t('common_delete_kpi_confirmation'),
      variant: 'warning',
      onConfirm: () => deleteDeliverableMutation.mutate(),
    });
  };

  const handleCopyLink = async () => {
    await navigator.clipboard.writeText(window.location.href);
    toast.add({
      type: 'success',
      title: t('common_copied_to_clipboard'),
      description: t('common_text_copied_to_clipboard'),
    });
  };

  const updateDeliverableStatusMutation = useMutation({
    mutationFn: async (status: string) => {
      await deliverablesService.updateDeliverable(
        catalogItemId as string,
        {
          isActive: status === 'INACTIVE' ? false : true,
        } as DeliverableItem,
      );
    },
    onSuccess: async (_, status) => {
      actionModal.closeModal();
      successModal.openModal({
        title: t('common_congratulations'),
        message:
          status === 'INACTIVE'
            ? t('common_deliverable_status_inactive_successfully')
            : t('common_deliverable_status_active_successfully'),
        variant: 'success',
        onUnderstood: () => {
          successModal.closeModal();
        },
      });
      await queryClient.invalidateQueries({
        queryKey: ['getDeliverableDetails', catalogItemId],
      });
      await queryClient.fetchQuery({
        queryKey: ['getDeliverableDetails', catalogItemId],
        queryFn: () =>
          deliverablesService.getDeliverableById(catalogItemId as string),
      });
    },
    onError: error => {
      actionModal.closeModal();
      toast.add({
        type: 'error',
        title: t('common_error_updating_kpi'),
        description: error instanceof Error ? error.message : 'Unknown error',
      });
    },
  });

  const handleupdateDeliverableStatus = (status: string) => {
    if (!status) {
      return;
    }
    actionModal.openModal({
      title: t('common_warning'),
      message:
        status === 'INACTIVE'
          ? t('common_deliverable_status_inactive_confirmation')
          : t('common_deliverable_status_active_confirmation'),
      variant: 'warning',
      onConfirm: () => updateDeliverableStatusMutation.mutate(status),
    });
  };

  const deleteDeliverableMutation = useMutation({
    mutationFn: () =>
      deliverablesService.deleteDeliverable(catalogItemId as string),
    onSuccess: () => {
      actionModal.closeModal();
      successModal.openModal({
        title: t('common_congratulations'),
        message: t('common_kpi_deleted_description'),
        variant: 'success',
        onUnderstood: async () => {
          successModal.closeModal();
          await router.push('/');
        },
      });
    },
    onError: error => {
      actionModal.closeModal();
      toast.add({
        type: 'error',
        title: 'Error Deleting KPI',
        description: error instanceof Error ? error.message : 'Unknown error',
      });
    },
  });

  return {
    catalogItemId,
    data: kpiDataFiltered ? kpiData : data ,
    transformedData: {
      items: transformedData || [],
      isActive: (kpiDataFiltered ? kpiData : data)?.isActive,
      deliverableType: kpiDataFiltered ? kpiData?.type : data?.type,
    },
    isLoading,
    handleNavigateBack,
    handleDownloadSnapshot,
    handleCopyLink,
    handledeleteDeliverable,
    isLoadingDelete: deleteDeliverableMutation.isLoading,
    isLoadingToggle: updateDeliverableStatusMutation.isLoading,
    handleNavigateToEdit,
    gridRef,
    contentRef,
    actionModal,
    successModal,
    handleupdateDeliverableStatus,
  };
}
