import { GetStaticProps } from 'next';

import * as Home from '~/entities/Home';
import { DeliverableType } from '~/entities/Home/components/Filters/types';
import deliverablesService from '~/shared/services/deliverables';
import { DeliverableItemsResponse } from '~/shared/types/DeliverablesService';

interface IndexProps {
  initialKpis: DeliverableItemsResponse;
}

export default function Index({ initialKpis }: IndexProps) {
  return <Home.Page initialKpis={initialKpis} />;
}

export const getStaticProps: GetStaticProps<IndexProps> = async () => {
  try {
    const initialKpis = await deliverablesService.getDeliverables({
      pageNumber: 1,
      pageSize: 10,
      deliverableTypes: [
        DeliverableType.KPI,
        DeliverableType.PROJECT_YES_NO,
        DeliverableType.PROJECT,
        DeliverableType.MASTER_PROJECT,
      ],
    });

    return {
      props: {
        initialKpis,
      },
    };
  } catch (error) {
    console.error('Error fetching initial data:', error);

    return {
      props: {
        initialKpis: {
          data: [],
          pageNumber: 1,
          pageSize: 10,
          totalRecords: 0,
        },
      },
    };
  }
};
